import math
from typing import Callable, Optional, Tuple
import pandas as pd
import numpy as np
from itertools import chain, dropwhile, takewhile

from electrochemistry.prelithiation import Prelithiation, PrelithiationProcess
from models.chart_model import ChartDatasetModel, ChartModel
from models.table_model import TableCellModel, TableRowModel, TableModel
from utils.interpolate import interpolate

class Formation:

    description: str
    cycle: Optional[pd.DataFrame]
    capacity: Optional[float]

    u_min: Optional[float]
    u_max: Optional[float]

    q_min: Optional[float]
    q_max: Optional[float]

    u_nom: Optional[float]

    def __init__(self, description: str, cycle: Optional[pd.DataFrame]):
        self.description = description
        self.cycle = cycle

        if self.cycle is not None:

            self.u_min = self.cycle['SpannungV'].min()
            self.u_max = self.cycle['SpannungV'].max()
            self.q_min = self.cycle['LadungMAhg'].min()
            self.q_max = self.cycle['LadungMAhg'].max()
            self.u_nom = self.get_u_nom()
            if self.description == 'Formierung 1. Laden':
                self.capacity = self.q_max
            else:
                self.capacity = self.q_max - self.q_min
        else:
            self.capacity = None
            self.u_min = None
            self.u_max = None
            self.q_min = None
            self.q_max = None
            self.u_nom = None

    def from_list(description: str, cycle_list: Optional[list[dict[str, float]]]):
        cycle = pd.DataFrame({
            'LadungMAhg': [item['LadungMAhg'] for item in cycle_list],
            'SpannungV': [item['SpannungV'] for item in cycle_list]
        }) if cycle_list is not None else None

        return Formation(description, cycle)
    
    def get_u_slope(self) -> float:
        return (self.get_u_end() - self.get_u_start()) / (self.get_q_end() - self.get_q_start())
    
    def get_u_mean_diff(self) -> float:
        # return self.cycle['SpannungV'].diff().mean()
        # this should be the same
        return (self.cycle['SpannungV'].iloc[-1] - self.cycle['SpannungV'].iloc[0]) / len(self.cycle.index)

    def is_u_increasing(self) -> bool:
        return self.get_u_mean_diff() > 0

    def is_u_decreasing(self) -> bool:
        return self.get_u_mean_diff() < 0

    def get_u_start(self) -> float:
        return self.cycle['SpannungV'].iloc[0]

    def get_u_end(self) -> float:
        return self.cycle['SpannungV'].iloc[-1]
    
    def get_q_slope(self) -> float:
        return (self.get_q_end() - self.get_q_start()) / (self.get_u_end() - self.get_u_start())

    def get_q_mean_diff(self) -> float:
        # return self.cycle['LadungMAhg'].diff().mean()
        # this should be the same
        return (self.cycle['LadungMAhg'].iloc[-1] - self.cycle['LadungMAhg'].iloc[0]) / len(self.cycle.index)

    def is_q_increasing(self) -> bool:
        return self.get_q_mean_diff() > 0

    def is_q_decreasing(self) -> bool:
        return self.get_q_mean_diff() < 0

    def get_q_start(self) -> float:
        return self.cycle['LadungMAhg'].iloc[0]

    def get_q_end(self) -> float:
        return self.cycle['LadungMAhg'].iloc[-1]
    
    def get_q(self, u: float) -> float:
        return interpolate([u], self.cycle['SpannungV'], self.cycle['LadungMAhg'])[0]
    
    def get_u(self, q: float) -> float:
        return interpolate([q], self.cycle['LadungMAhg'], self.cycle['SpannungV'])[0]

    def redistribute_u(self, sample_count:int=None):
        if self.cycle is None:
            return Formation(self.description, None)
        if sample_count is None or sample_count < len(self.cycle["SpannungV"]):
            sample_count = len(self.cycle["SpannungV"])

        sample_arr = pd.Series(range(0, sample_count)) * ((self.u_max - self.u_min) / (sample_count - 1)) + self.u_min
        if self.is_u_decreasing():
            sample_arr = sample_arr[::-1]
        q_temp = interpolate(sample_arr, self.cycle['SpannungV'], self.cycle['LadungMAhg'])

        cycle = pd.DataFrame({
                'LadungMAhg': q_temp,
                'SpannungV': sample_arr
            }).reset_index()
        return Formation(self.description, cycle)
    

    def redistribute_q(self, sample_count:int=None):
        if self.cycle is None:
            return Formation(self.description, None)
        if sample_count is None or sample_count < len(self.cycle["LadungMAhg"]):
            sample_count = len(self.cycle["LadungMAhg"])

        sample_arr = pd.Series(range(0, sample_count)) * ((self.q_max - self.q_min) / (sample_count - 1)) + self.q_min
        if self.is_q_decreasing():
            sample_arr = sample_arr[::-1]
        q_temp = interpolate(sample_arr, self.cycle['LadungMAhg'], self.cycle['SpannungV'])

        cycle = pd.DataFrame({
                'LadungMAhg': sample_arr,
                'SpannungV': q_temp
            }).reset_index()
        return Formation(self.description, cycle)

    def monotonize_u(self):
        if self.cycle is not None:
            if self.is_u_decreasing():
                u_indices = self.cycle['SpannungV'] < self.cycle['SpannungV'].expanding().min().shift(1, fill_value = 10000)
                u_temp = self.cycle['SpannungV'].loc[u_indices]
                q_temp = self.cycle['LadungMAhg'].loc[u_indices]
            else:
                u_indices = self.cycle['SpannungV'] > self.cycle['SpannungV'].expanding().max().shift(1, fill_value = -10000)
                u_temp = self.cycle['SpannungV'].loc[u_indices]
                q_temp = self.cycle['LadungMAhg'].loc[u_indices]

            if len(q_temp) > 0:
                if self.is_q_decreasing():
                    q_temp.iat[-1] = self.cycle['LadungMAhg'].min()
                else:
                    q_temp.iat[-1] = self.cycle['LadungMAhg'].max()

            cycle = pd.DataFrame({
                'LadungMAhg': q_temp,
                'SpannungV': u_temp
            }).reset_index()
        else:
            cycle = None

        return Formation(self.description, cycle)

    def monotonize_q(self):
        if self.cycle is not None:
            if self.is_q_decreasing():
                q_indices = self.cycle['LadungMAhg'] < self.cycle['LadungMAhg'].expanding().min().shift(1, fill_value = 10000)
                q_temp = self.cycle['LadungMAhg'].loc[q_indices]
                u_temp = self.cycle['SpannungV'].loc[q_indices]
            else:
                q_indices = self.cycle['LadungMAhg'] > self.cycle['LadungMAhg'].expanding().max().shift(1, fill_value = -10000)
                q_temp = self.cycle['LadungMAhg'].loc[q_indices]
                u_temp = self.cycle['SpannungV'].loc[q_indices]

            if len(u_temp) > 0:
                if self.is_u_decreasing():
                    u_temp.iat[-1] = self.u_min
                else:
                    u_temp.iat[-1] = self.u_max

            cycle = pd.DataFrame({
                'LadungMAhg': q_temp,
                'SpannungV': u_temp
            }).reset_index()
        else:
            cycle = None

        return Formation(self.description, cycle)

    def monotonize_all(self):
        return self.monotonize_u().monotonize_q()

    def scale_q(self, factor: float):
        if self.cycle is not None:
            cycle = self.cycle.copy(deep=False)
            cycle['LadungMAhg'] = cycle['LadungMAhg'] * factor
        else:
            cycle = None

        return Formation(self.description, cycle)

    def translate_q(self, offset: float):
        if self.cycle is not None:
            cycle = self.cycle.copy(deep=False)
            cycle['LadungMAhg'] = cycle['LadungMAhg'] + offset
        else:
            cycle = None

        return Formation(self.description, cycle)
    
    def apply_prelithiation(self, prelithiation: Prelithiation):
        if self.cycle is not None:
            cycle = self.cycle.copy(deep=False)
            if prelithiation.capacity is not None:
                if prelithiation.process == PrelithiationProcess.GROUP_14:
                    cycle['LadungMAhg'] = (cycle['LadungMAhg'] - prelithiation.capacity) / (1 + prelithiation.get_lithium_weight())
                else:
                    cycle['LadungMAhg'] = cycle['LadungMAhg'] - prelithiation.capacity
        else:
            cycle = None

        return Formation(self.description, cycle)

    def get_u_nom(self) -> float:
        not_nan_indices = self.cycle['SpannungV'].notna()
        return math.fabs(1 / (self.q_max - self.q_min) * np.trapz(self.cycle['SpannungV'].loc[not_nan_indices], self.cycle['LadungMAhg'].loc[not_nan_indices]))
    
    def get_chart_color(self) -> str:
        match self.description:
            case 'Formierung 1. Laden':
                return '#0072BD'
            case 'Formierung 1. Entladen':
                return '#0072BD'
            case 'Formierung 2. Laden':
                return '#0072BD'
            case 'C/10 Laden':
                return '#D95319'
            case 'C/10 Entladen':
                return '#EDB120'
            case 'C/3 Entladen':
                return '#7E2F8E'
            case 'C/2 Entladen':
                return '#88B64A'
            case '1C Entladen':
                return '#4DBEEE'
            case _:
                return '#969992'

    def get_chart_dataset(self, id: Optional[str], samples: list[float], prefix: Optional[str] = None, dash: Optional[list[int]] = None, hidden: Optional[bool] = None) -> ChartDatasetModel:
        if self.cycle is not None:
            cycle = self.cycle.copy().sort_values('LadungMAhg')
            samples = list(chain([self.q_min], takewhile(lambda v: v < self.q_max, dropwhile(lambda v: v <= self.q_min, samples)), [self.q_max]))
            values = interpolate(samples, cycle['LadungMAhg'], cycle['SpannungV'], )
            data = [{ 'x': t[0], 'y': t[1] } for t in zip(samples, values)]
        else:
            data = None

        return {
            'id' : id,
            'description': self.description,
            'label': self.description if prefix is None else f"{prefix}: {self.description}",
            'hidden': self.description not in ['Formierung 1. Laden', 'Formierung 1. Entladen', 'Formierung 2. Laden'] if hidden is None else hidden,
            'data': data,
            'border_color': self.get_chart_color(),
            'border_dash': dash if dash is not None else []
        }

class FormationTable:

    formations: list[Formation]

    q_min: Optional[float]
    q_max: Optional[float]

    def __init__(self, formations: list[Formation]):
        self.formations = formations
        self.q_min = min((f.q_min for f in self.formations if f.q_min is not None), default = None)
        self.q_max = max((f.q_max for f in self.formations if f.q_max is not None), default = None)

    def from_dict(formations_dict: dict[str, list]):
        return FormationTable([Formation.from_list(formations_dict['descriptions'][i], formations_dict['cycles'][i]) for i in range(len(formations_dict['descriptions']))])

    def get_formation(self, description: str) -> Formation:
        return next(filter(lambda f: f.description == description, self.formations), None)

    def get_formation_first_charge(self) -> Formation:
        return self.get_formation('Formierung 1. Laden')

    def get_formation_first_discharge(self) -> Formation:
        return self.get_formation('Formierung 1. Entladen')

    def get_formation_second_charge(self) -> Formation:
        return self.get_formation('Formierung 2. Laden')

    def get_formation_c_10_charge(self) -> Formation:
        return self.get_formation('C/10 Laden')

    def get_formation_c_10_discharge(self) -> Formation:
        return self.get_formation('C/10 Entladen')

    def get_formation_c_3_discharge(self) -> Formation:
        return self.get_formation('C/3 Entladen')

    def get_formation_c_2_discharge(self) -> Formation:
        return self.get_formation('C/2 Entladen')

    def get_formation_1_c_discharge(self) -> Formation:
        return self.get_formation('1C Entladen')
    
    def with_formation(self, description: str, formation: Formation):
        return FormationTable([(formation if description == f.description else f) for f in self.formations])
    
    def with_formation_first_charge(self, formation: Formation):
        return self.with_formation('Formierung 1. Laden', formation)

    def monotonize_u(self):
        return FormationTable([f.monotonize_u() for f in self.formations])

    def monotonize_q(self):
        return FormationTable([f.monotonize_q() for f in self.formations])

    def monotonize_all(self):
        return FormationTable([f.monotonize_all() for f in self.formations])
    
    def scale_q(self, factor: float):
        return FormationTable([f.scale_q(factor) for f in self.formations])
    
    def apply_prelithiation(self, prelithiation: Prelithiation):
        return FormationTable([f.apply_prelithiation(prelithiation) for f in self.formations])
    
    def get_formation_loss(self) -> float:
        return self.get_formation_first_charge().capacity - self.get_formation_first_discharge().capacity + self.get_formation_second_charge().capacity - self.get_formation_c_10_discharge().capacity

    def get_coulomb_efficiency(self) -> float:
        return 1 - (self.get_formation_loss() / self.get_formation_first_charge().capacity)
    
    def get_chart_data(self, n_samples: int) -> dict:

        formation_descriptions: list[tuple[str, str]] = [
            ('formation_first_charge', 'Formierung 1. Laden'),
            ('formation_first_discharge', 'Formierung 1. Entladen'),
            ('formation_second_charge', 'Formierung 2. Laden'),
            ('c10_charge', 'C/10 Laden'),
            ('c10_discharge', 'C/10 Entladen'),
            ('c3_discharge', 'C/3 Entladen'),
            ('c2_discharge', 'C/2 Entladen'),
            ('1c_discharge', '1C Entladen')
        ]

        samples: np.ndarray[float] = np.linspace(self.q_min, self.q_max, num = n_samples)
        return {
            'datasets': [self.get_formation(description[1]).get_chart_dataset(description[0], samples) for description in formation_descriptions]
        }
    
    def get_prefixed_chart_data(formation_tables: dict[str, tuple[str, 'FormationTable']], n_samples: int) -> ChartModel:
        
        formation_descriptions: list[tuple[str, str]] = [
            ('formation_first_charge', 'Formierung 1. Laden'),
            ('formation_first_discharge', 'Formierung 1. Entladen'),
            ('formation_second_charge', 'Formierung 2. Laden'),
            ('c10_charge', 'C/10 Laden'),
            ('c10_discharge', 'C/10 Entladen'),
            ('c3_discharge', 'C/3 Entladen'),
            ('c2_discharge', 'C/2 Entladen'),
            ('1c_discharge', '1C Entladen')
        ]

        q_min = min(t[1].q_min for t in formation_tables.values())
        q_max = max(t[1].q_max for t in formation_tables.values())
        samples: np.ndarray[float] = np.linspace(q_min, q_max, num = n_samples)

        keys = list(formation_tables.keys())
        return {
            'headers': [formation_tables[key][0] for key in keys],
            'datasets': [formation_tables[key][1].get_formation(description[1]).monotonize_q().get_chart_dataset(f'{key}_{description[0]}', samples, formation_tables[key][0], [5, 15] if (i > 0) else None, True if (i > 0) else None) for description in formation_descriptions for i, key in enumerate(keys)]
        }
    
    def get_balancing_summary_table(self) -> TableModel:
        return TableModel(
            headers = [ 'Beschreibung', 'Q', 'Unom' ],
            rows = [
                TableRowModel(cells = [TableCellModel(value = 'C/3 Entladen', unit = None), TableCellModel(value = self.get_formation_c_3_discharge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_c_3_discharge().u_nom, unit = 'V')]),
                TableRowModel(cells = [TableCellModel(value = 'C/10 Entladen', unit = None), TableCellModel(value = self.get_formation_c_10_discharge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_c_10_discharge().u_nom, unit = 'V')]),
                TableRowModel(cells = [TableCellModel(value = 'Formierung 1. Laden', unit = None), TableCellModel(value = self.get_formation_first_charge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_first_charge().u_nom, unit = 'V')]),
                TableRowModel(cells = [TableCellModel(value = 'Formierung 1. Entladen', unit = None), TableCellModel(value = self.get_formation_first_discharge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_first_discharge().u_nom, unit = 'V')]),
                TableRowModel(cells = [TableCellModel(value = 'Formierung 2. Laden', unit = None), TableCellModel(value = self.get_formation_second_charge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_second_charge().u_nom, unit = 'V')]),
                TableRowModel(cells = [TableCellModel(value = 'Formierungsverlust', unit= None), TableCellModel(value = self.get_formation_loss(), unit = 'mAh/g' )]),
                TableRowModel(cells = [TableCellModel(value = 'Coulomb Effizienz', unit = None ), TableCellModel(value = self.get_coulomb_efficiency() * 100, unit = '%' )]),
                TableRowModel(cells = [TableCellModel(value = 'C/10 Laden', unit = None), TableCellModel(value = self.get_formation_c_10_charge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_c_10_charge().u_nom, unit = 'V')]),
                TableRowModel(cells = [TableCellModel(value = 'C/2 Entladen', unit = None), TableCellModel(value = self.get_formation_c_2_discharge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_c_2_discharge().u_nom, unit = 'V')]),
                TableRowModel(cells = [TableCellModel(value = '1C Entladen', unit = None), TableCellModel(value = self.get_formation_1_c_discharge().capacity, unit = 'mAh/g'), TableCellModel(value = self.get_formation_1_c_discharge().u_nom, unit = 'V')]),
            ]
        )

    def get_prefixed_summary_table(formation_tables: dict[str, 'FormationTable'], u_min: float | None, u_max: float | None) -> TableModel:

        def get_q_values_at_voltage_range(formation: Formation, u_min: float, u_max: float) -> tuple[float, float]:
            """Get q_max and q_min values by interpolating at specified voltage range"""
            if formation is None:
                return (0.0, 0.0)

            monotonized = formation.monotonize_u()

            # Get charge values at both voltage limits
            q_at_u_max = monotonized.get_q(u_max)
            q_at_u_min = monotonized.get_q(u_min)

            # q_max should be the higher charge value, q_min should be the lower charge value
            # regardless of which voltage they correspond to
            q_max_at_range = max(q_at_u_max, q_at_u_min)
            q_min_at_range = min(q_at_u_max, q_at_u_min)

            return (q_max_at_range, q_min_at_range)

        keys = list(formation_tables.keys())

        return {
            'headers': [ 'Beschreibung' ] + keys,
            'rows': [
                { 'cells': [{ 'value': 'C/3 Entladen', 'unit': None }] + [{ 'value': formation_tables[k].get_formation('C/3 Entladen').capacity, 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'C/10 Entladen', 'unit': None }] + [{ 'value':
                                                                            (
                                                                                # Use direct q_max/q_min if no voltage range specified
                                                                                formation_tables[k].get_formation('C/10 Entladen').q_max
                                                                                    if u_max is None else
                                                                                # Calculate q_max/q_min at specified voltage range
                                                                                get_q_values_at_voltage_range(formation_tables[k].get_formation('C/10 Entladen'), u_min, u_max)[0]
                                                                            )
                                                                            -
                                                                            (
                                                                                formation_tables[k].get_formation('C/10 Entladen').q_min
                                                                                    if u_min is None else
                                                                                get_q_values_at_voltage_range(formation_tables[k].get_formation('C/10 Entladen'), u_min, u_max)[1]
                                                                            ), 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'Formierung 1. Laden', 'unit': None }] + [{ 'value': formation_tables[k].get_formation('Formierung 1. Laden').capacity, 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'Formierung 1. Entladen', 'unit': None }] + [{ 'value':
                                                                            formation_tables[k].get_formation('Formierung 1. Entladen').q_max - formation_tables[k].get_formation('Formierung 1. Entladen').q_min
                                                                                if u_max is None else
                                                                            get_q_values_at_voltage_range(formation_tables[k].get_formation('Formierung 1. Entladen'), u_min, u_max)[0] - get_q_values_at_voltage_range(formation_tables[k].get_formation('Formierung 1. Entladen'), u_min, u_max)[1], 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'Formierung 2. Laden', 'unit': None }] + [{ 'value': formation_tables[k].get_formation('Formierung 2. Laden').capacity, 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'C/10 Laden', 'unit': None }] + [{ 'value': formation_tables[k].get_formation('C/10 Laden').capacity, 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'C/2 Entladen', 'unit': None }] + [{ 'value': formation_tables[k].get_formation('C/2 Entladen').capacity, 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': '1C Entladen', 'unit': None }] + [{ 'value': formation_tables[k].get_formation('1C Entladen').capacity, 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'Formierungsverlust', 'unit': None }] + [{ 'value': formation_tables[k].get_formation_loss(), 'unit': 'mAh/g' } for k in keys]},
                { 'cells': [{ 'value': 'Coulomb Effizienz', 'unit': None }] + [{ 'value': formation_tables[k].get_coulomb_efficiency() * 100, 'unit': '%' } for k in keys]}
            ]
        }
